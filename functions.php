<?php
// Add custom Theme Functions here
//
// Add date picker field
add_action('woocommerce_before_add_to_cart_button', 'add_custom_date_field');
function add_custom_date_field()
{
    global $product;

    // Only show for variable products
    if (!$product || !$product->is_type('variable')) {
        return;
    }

    // Check if it's a VIN product (only show date picker for VIN products)
    $is_vin_product = get_post_meta($product->get_id(), 'vin_product', true);
    // Check if it's a Sunworld product
    $is_sunworld_product = get_post_meta($product->get_id(), 'sunworld_product', true);

    // If it's not a VIN product OR a Sunworld product, don't show the date picker
    if ($is_vin_product !== 'yes' && $is_sunworld_product !== 'yes') {
        return;
    }

    // Check if any variation has the *required* API pricing attributes for the product type
    $has_api_pricing = false;
    $required_fields_message = ''; // Default message
    $variations = $product->get_available_variations();

    if (!empty($variations)) {
        foreach ($variations as $variation_data) {
            $variation_id = $variation_data['variation_id'];
            $variation = wc_get_product($variation_id);

            if (!$variation) {
                continue;
            }

            $site_code = $variation->get_meta('site_code');

            if ($is_sunworld_product === 'yes') {
                // Sunworld requires Site Code and Product Code
                $product_code = $variation->get_meta('product_code');
                if (!empty($site_code) && !empty($product_code)) {
                    $has_api_pricing = true;
                    break; // Found one valid variation
                }
                $required_fields_message = 'Please configure Site Code and Product Code for each variation.';
            } elseif ($is_vin_product === 'yes') {
                // Vinpearl requires Site Code, Service Code, and Rate Code
                $service_code = $variation->get_meta('service_code');
                $rate_code = $variation->get_meta('rate_code');
                if (!empty($site_code) && !empty($service_code) && !empty($rate_code)) {
                    $has_api_pricing = true;
                    break; // Found one valid variation
                }
                $required_fields_message = 'Please configure Site Code, Service Code, and Rate Code for each variation.';
            }
        }
    }

    if ($has_api_pricing) {
        // Get min date based on current time
        echo '<div class="custom-date-field">
            <label>Chọn Ngày Sử Dụng: </label>
            <input type="date" id="custom-date" name="custom_date" required>
            <div id="api-price-loading" style="display:none;">Checking price availability...</div>
        </div>';

        // Add script to disable the Add to Cart button until a date is selected
        // and to handle date restrictions
        echo '<script type="text/javascript">
            jQuery(document).ready(function($) {
                // Set minimum date for date picker
                function setMinDate() {
                    var today = new Date();
                    var minDate = today.toISOString().split("T")[0];

                    // Set the min attribute
                    $("#custom-date").attr("min", minDate);

                    // If current value is before min date, clear it
                    var currentValue = $("#custom-date").val();
                    if (currentValue && currentValue < minDate) {
                        $("#custom-date").val("");
                    }
                }

                // Set min date on page load
                setMinDate();

                // Initially disable the Add to Cart button
                $(".single_add_to_cart_button").prop("disabled", true);
                $(".single_add_to_cart_button").addClass("disabled");

                // Add a message explaining why the button is disabled
                if ($(".date-required-notice").length === 0) {
                    $(".single_add_to_cart_button").after("<p class=\'date-required-notice\' style=\'color: #e2401c; margin-top: 10px;\'>Please select a date before adding to cart</p>");
                }

                // Enable the button when a date is selected and a variation is chosen
                $("#custom-date").on("change", function() {
                    checkEnableButton();
                });

                $(".variations select").on("change", function() {
                    checkEnableButton();
                });

                function checkEnableButton() {
                    var dateSelected = $("#custom-date").val();
                    var variationSelected = $(".variation_id").val() && $(".variation_id").val() !== "0";

                    if (dateSelected && variationSelected) {
                        $(".single_add_to_cart_button").prop("disabled", false);
                        $(".single_add_to_cart_button").removeClass("disabled");
                        $(".date-required-notice").hide();
                    } else {
                        $(".single_add_to_cart_button").prop("disabled", true);
                        $(".single_add_to_cart_button").addClass("disabled");
                        $(".date-required-notice").show();
                    }
                }
            });
        </script>';
    } else {
        // For admin users, show debug message if no variations have the required fields
        if (current_user_can('manage_options') && !empty($variations)) {
            // Use the specific message determined earlier
            if (empty($required_fields_message)) {
                // Fallback if product type wasn't determined correctly (shouldn't happen)
                $required_fields_message = 'This product has variations, but they are missing the required API pricing fields.';
            }
            echo '<div class="woocommerce-info">Admin notice: ' . esc_html($required_fields_message) . '</div>';
        }
    }
}

// Add variation metadata including API pricing attributes
add_filter('woocommerce_available_variation', 'add_variation_metadata', 10, 3);
function add_variation_metadata($data, $product, $variation)
{
    // Get custom attributes for API pricing
    $site_code = $variation->get_meta('site_code');
    $service_code = $variation->get_meta('service_code');
    $rate_code = $variation->get_meta('rate_code');
    $product_code = $variation->get_meta('product_code'); // Assuming meta key 'product_code'

    // Get parent product's sunworld status
    $parent_product = wc_get_product($product->get_id());
    $is_sunworld_product = false;
    if ($parent_product) {
        $is_sunworld_product = $parent_product->get_meta('sunworld_product', true) === 'yes';
    }

    // Add to variation data
    if ($site_code) {
        $data['site_code'] = $site_code;
    }
    if ($service_code) {
        $data['service_code'] = $service_code;
    }
    if ($rate_code) {
        $data['rate_code'] = $rate_code;
    }
    if ($product_code) {
        $data['product_code'] = $product_code;
    }
    $data['is_sunworld_product'] = $is_sunworld_product;

    return $data;
}

// Add custom date to cart item data
add_filter('woocommerce_add_cart_item_data', 'add_custom_date_to_cart_item', 10, 3);
function add_custom_date_to_cart_item($cart_item_data, $product_id, $variation_id)
{
    if (isset($_POST['custom_date'])) {
        $cart_item_data['custom_date'] = sanitize_text_field($_POST['custom_date']);

        // Store original API price (not discounted) from AJAX
        if (isset($_POST['api_price']) && is_numeric($_POST['api_price'])) {
            $price = floatval($_POST['api_price']);
            $cart_item_data['api_price'] = $price;

            // Also store already discounted price if available (for reference only)
            if (isset($_POST['api_discounted_price']) && is_numeric($_POST['api_discounted_price'])) {
                $discounted_price = floatval($_POST['api_discounted_price']);
                $cart_item_data['api_discounted_price'] = $discounted_price;
            }
        } else {
            // Log functional error - price expected but not found in POST
            error_log('[Add to Cart] No API price found in POST data for product ' . $product_id . ($variation_id ? ' (Var: ' . $variation_id . ')' : ''));
        }

        // Store cost_of_good if available from AJAX
        if (isset($_POST['cost_of_good']) && is_numeric($_POST['cost_of_good'])) {
            $cart_item_data['cost_of_good'] = floatval($_POST['cost_of_good']);
        } else {
            // Optional: Log if cost_of_good is expected but not found
            // error_log('[Add to Cart] No Cost of Good found in POST data for product ' . $product_id . ($variation_id ? ' (Var: ' . $variation_id . ')' : ''));
        }

        // Add a unique key based on the date to prevent items with different dates from being combined
        $cart_item_data['unique_key'] = md5($product_id . $variation_id . $cart_item_data['custom_date']);
    }

    return $cart_item_data;
}

// Display custom date in cart and checkout
add_filter('woocommerce_get_item_data', 'display_custom_date_in_cart', 10, 2);
function display_custom_date_in_cart($item_data, $cart_item)
{
    if (isset($cart_item['custom_date'])) {
        $item_data[] = [
            'key' => __('Ngày Sử Dụng', 'woocommerce'),
            'value' => date_i18n(get_option('date_format'), strtotime($cart_item['custom_date'])),
        ];
    }
    return $item_data;
}

// Add custom date to order item meta
add_action('woocommerce_checkout_create_order_line_item', 'add_custom_date_to_order_item_meta', 10, 4);
function add_custom_date_to_order_item_meta($item, $cart_item_key, $values, $order)
{
    if (isset($values['custom_date'])) {
        $item->add_meta_data(__('Ngày Sử Dụng', 'woocommerce'), date_i18n(get_option('date_format'), strtotime($values['custom_date'])));
        $item->add_meta_data('_custom_date', $values['custom_date'], true);

        // Store the API price if it exists
        if (isset($values['api_price'])) {
            $item->add_meta_data('_api_price', $values['api_price'], true);
        }
    }

    // Always save cost_of_good (if available) as cost of goods for WooCommerce reports
    if (isset($values['cost_of_good'])) {
        $item->add_meta_data('_wc_cog_item_cost', $values['cost_of_good'], true);
        $product_id = $item->get_product_id();
        // Update post meta for the product itself. 
        // Consider if this should be variation_id if the item is a variation and COG differs.
        // For simplicity, using product_id as per original logic with api_price.
        update_post_meta($product_id, '_wc_cog_cost', $values['cost_of_good']); 
    }
}

// Apply the API price to cart items (Potentially redundant with wdp_calculate_base_price, review if needed)
add_filter('woocommerce_product_get_price', 'apply_api_price_to_product', 99, 2);
add_filter('woocommerce_product_variation_get_price', 'apply_api_price_to_product', 99, 2);
function apply_api_price_to_product($price, $product)
{
    global $woocommerce;

    // Get cart
    $cart = WC()->cart;
    if (empty($cart)) {
        return $price;
    }

    // Check if this product is in cart with API price
    foreach ($cart->get_cart() as $cart_item) {
        $product_id = $product->get_id();
        $in_cart = false;

        // Check if this is the product in the cart
        if ($cart_item['product_id'] == $product_id) {
            $in_cart = true;
        }

        // For variations, check both the variation ID and the parent product ID
        if ($product->is_type('variation')) {
            if ($cart_item['variation_id'] == $product_id || $cart_item['product_id'] == $product->get_parent_id()) {
                $in_cart = true;
            }
        }

        // Apply the API price if found
        if ($in_cart && isset($cart_item['api_price'])) {
            // Removed log: error_log('Applying API price in cart for product #' . $product_id . ': ' . $cart_item['api_price']);
            return $cart_item['api_price'];
        }
    }

    return $price;
}

// Add hook to ensure the API price is applied when calculating cart item price (Display - Potentially redundant)
add_filter('woocommerce_cart_item_price', 'apply_api_price_to_cart_item', 99, 3);
function apply_api_price_to_cart_item($price_html, $cart_item, $cart_item_key)
{
    // Prioritize showing the final discounted price if available
    if (isset($cart_item['api_discounted_price']) && is_numeric($cart_item['api_discounted_price'])) {
        // Removed log: error_log('Formatting cart item price with final discounted API price: ' . $cart_item['api_discounted_price']);
        return wc_price($cart_item['api_discounted_price']);
    }
    // Fallback to original API price if discounted one isn't set
    elseif (isset($cart_item['api_price']) && is_numeric($cart_item['api_price'])) {
        // Removed log: error_log('Formatting cart item price with original API price: ' . $cart_item['api_price']);
        return wc_price($cart_item['api_price']);
    }
    return $price_html;
}

// Apply API price to line subtotal (Display - Potentially redundant)
add_filter('woocommerce_cart_item_subtotal', 'apply_api_price_to_subtotal', 99, 3);
function apply_api_price_to_subtotal($subtotal, $cart_item, $cart_item_key)
{
    // Prioritize using the final discounted price for subtotal
    $price_to_use = null;
    if (isset($cart_item['api_discounted_price']) && is_numeric($cart_item['api_discounted_price'])) {
        $price_to_use = $cart_item['api_discounted_price'];
        // Removed log: error_log('Calculating subtotal with final discounted API price: ' . $price_to_use);
    }
    // Fallback to original API price if discounted one isn't set
    elseif (isset($cart_item['api_price']) && is_numeric($cart_item['api_price'])) {
        $price_to_use = $cart_item['api_price'];
        // Removed log: error_log('Calculating subtotal with original API price: ' . $price_to_use);
    }

    if (!is_null($price_to_use)) {
        $quantity = $cart_item['quantity'];
        return wc_price($price_to_use * $quantity);
    }

    return $subtotal;
}

// Add AJAX endpoint to fetch price from API
add_action('wp_ajax_get_api_price', 'get_api_price');
add_action('wp_ajax_nopriv_get_api_price', 'get_api_price');
function get_api_price()
{
    // Removed log: error_log('API Price AJAX request received: ' . print_r($_POST, true));

    // Verify nonce for security
    check_ajax_referer('api_price_nonce', 'security');

    // Get posted data
    $date = isset($_POST['date']) ? sanitize_text_field($_POST['date']) : '';
    $site_code = isset($_POST['site_code']) ? sanitize_text_field($_POST['site_code']) : ''; // Use sanitize_text_field for codes too
    $service_code = isset($_POST['service_code']) ? sanitize_text_field($_POST['service_code']) : '';
    $rate_code = isset($_POST['rate_code']) ? sanitize_text_field($_POST['rate_code']) : '';
    $product_code = isset($_POST['product_code']) ? sanitize_text_field($_POST['product_code']) : ''; // Get product_code
    $is_sunworld = isset($_POST['is_sunworld']) && filter_var($_POST['is_sunworld'], FILTER_VALIDATE_BOOLEAN); // Get is_sunworld flag
    $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;
    $variation_id = isset($_POST['variation_id']) ? intval($_POST['variation_id']) : 0;
    $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;

    // Default API price
    $api_price = null;
    $cost_of_good = null; // Initialize cost_of_good

    // Validate essential parameters
    if (empty($date) || empty($site_code)) {
        error_log('[API Price Check] Missing Date or Site Code for product ' . $product_id . ($variation_id ? ' (Var: ' . $variation_id . ')' : ''));
        wp_send_json_error(['message' => 'Missing required parameters (Date, Site Code).']);
        return;
    }

    if ($is_sunworld) {
        // --- Handle Sunworld API ---

        if (empty($product_code)) {
            error_log('[API Price Check - Sunworld] Missing Product Code for product ' . $product_id . ' (Var: ' . $variation_id . ')');
            wp_send_json_error(['message' => 'Missing required product code for Sunworld.']);
            return;
        }

        // Sunworld API needs date in Y-m-d format (assumed)
        $api_date = date('Y-m-d', strtotime($date));
        
        $all_results = [];
        $current_api_page = 1;
        $per_page = 50; // Max per page
        $total_pages = 1; // Initialize, will be updated by API response

        do {
            $api_url = add_query_arg(
                [
                    'siteCodes' => $site_code,
                    'date' => $api_date,
                    'page' => $current_api_page,
                    'per_page' => $per_page,
                ],
                'https://voucherapi.qqfoodie.com/sunworld-ota/products'
            );

            error_log('[API Price Check - Sunworld] Requesting URL (Page ' . $current_api_page . '): ' . $api_url); // Log URL for debugging

            $response = wp_remote_get($api_url, [
                'timeout' => 30, // Increased timeout slightly for potentially larger combined requests
            ]);

            if (is_wp_error($response)) {
                error_log('[API Price Check - Sunworld] Request failed for ' . $api_url . '. Error: ' . $response->get_error_message());
                wp_send_json_error(['message' => 'Sunworld API request failed: ' . $response->get_error_message()]);
                return; // Exit if any page fails
            }

            $response_code = wp_remote_retrieve_response_code($response);
            $body = wp_remote_retrieve_body($response);

            if ($response_code != 200) {
                error_log('[API Price Check - Sunworld] API (' . $api_url . ') returned error code: ' . $response_code . '. Body: ' . $body);
                wp_send_json_error(['message' => 'Sunworld API returned error code: ' . $response_code]);
                return; // Exit if any page returns an error
            }

            $data = json_decode($body, true);

            if (empty($data) || !isset($data['success']) || $data['success'] !== true || !isset($data['result']) || !is_array($data['result'])) {
                error_log('[API Price Check - Sunworld] Invalid response structure from ' . $api_url . '. Body: ' . $body);
                wp_send_json_error(['message' => 'Invalid response from Sunworld API.']);
                return; // Exit on invalid structure
            }

            if (isset($data['result']) && is_array($data['result'])) {
                $all_results = array_merge($all_results, $data['result']);
            }

            if (isset($data['paginationData']['current']) && isset($data['paginationData']['pageCount'])) {
                // Use 'current' from response as it's the authoritative current page number just fetched
                $total_pages = (int)$data['paginationData']['pageCount']; 
                // Increment for the *next* page request
                $current_api_page = (int)$data['paginationData']['current'] + 1; 
            } else {
                error_log('[API Price Check - Sunworld] Pagination data missing or incomplete in response from ' . $api_url . '. Assuming single page. Body: ' . $body);
                $total_pages = $current_api_page; // Stop the loop
            }

        } while ($current_api_page <= $total_pages);


        // Find the matching product by product_code in the aggregated $all_results
        foreach ($all_results as $result_item) {
            if (isset($result_item['code']) && $result_item['code'] == $product_code && isset($result_item['unitPrice'])) {
                $api_price = $result_item['unitPrice'];
                if (isset($result_item['unitPrice'])) { // Check for unitPrice
                    $cost_of_good = $result_item['unitPrice'];
                }
                error_log('[API Price Check - Sunworld] Found matching product code ' . $product_code . ' with publicPrice: ' . $api_price . ($cost_of_good !== null ? ' and unitPrice: ' . $cost_of_good : '') . ' after checking all pages.');
                break; // Found the price
            }
        }

        if (is_null($api_price)) {
            error_log('[API Price Check - Sunworld] Product code ' . $product_code . ' not found in API response for date ' . $api_date . ' and site ' . $site_code . ' after checking all pages. Total items checked: ' . count($all_results));
            wp_send_json_error(['message' => 'Price not found for this variation on the selected date.']);
            return;
        }
    } else {
        // --- Handle Vinpearl API (Existing Logic) ---

        if (empty($service_code) || empty($rate_code)) {
            error_log('[API Price Check - Vinpearl] Missing Service or Rate Code for product ' . $product_id . ' (Var: ' . $variation_id . ')');
            wp_send_json_error(['message' => 'Missing required Service/Rate code for Vinpearl.']);
            return;
        }

        // Vinpearl API needs date in d/m/Y format
        $api_date = date('d/m/Y', strtotime($date));
        $api_url = 'https://voucherapi.qqfoodie.com/ota/rate-services';

        $request_data = [
            'ChannelCode' => 'OTA',
            'Date' => $api_date,
            'SiteCode' => $site_code, // Assuming Vinpearl uses integer site code
            'LanguageCode' => 'en',
            'Services' => [
                [
                    'ServiceCode' => $service_code,
                    'Number' => $quantity,
                    'RateCode' => $rate_code,
                ],
            ],
        ];

        error_log('[API Price Check - Vinpearl] Request to ' . $api_url . ': ' . json_encode($request_data));

        $response = wp_remote_post($api_url, [
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'body' => json_encode($request_data),
            'timeout' => 15,
        ]);

        if (is_wp_error($response)) {
            error_log('[API Price Check - Vinpearl] Request failed for ' . $api_url . '. Error: ' . $response->get_error_message());
            wp_send_json_error(['message' => 'Vinpearl API request failed: ' . $response->get_error_message()]);
            return;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        if ($response_code != 200 && $response_code != 201) {
            error_log('[API Price Check - Vinpearl] API (' . $api_url . ') returned error code: ' . $response_code . '. Body: ' . $body);
            wp_send_json_error(['message' => 'Vinpearl API returned error code: ' . $response_code]);
            return;
        }

        $data = json_decode($body, true);

        if (!empty($data) && is_array($data)) {
            foreach ($data as $rate) {
                if (isset($rate['Services']) && is_array($rate['Services'])) {
                    foreach ($rate['Services'] as $service) {
                        if (isset($service['BasePrice'])) {
                            $api_price = $service['BasePrice'];
                            if (isset($service['Price'])) { // Check for Price
                                $cost_of_good = $service['Price'];
                            }
                            error_log('[API Price Check - Vinpearl] API price found: ' . $api_price . ($cost_of_good !== null ? ' and COG Price: ' . $cost_of_good : ''));
                            break 2;
                        }
                    }
                }
            }
        }

        if (is_null($api_price)) {
            error_log(
                '[API Price Check - Vinpearl] API (' . $api_url . ') response did not contain BasePrice for product ' . $product_id . ($variation_id ? ' (Var: ' . $variation_id . ')' : '') . ' on date ' . $api_date . '. Body: ' . $body
            );
            wp_send_json_error(['message' => 'Price not available for the selected date (Vinpearl).']);
            return;
        }
    }

    // --- Common Logic: Discount Calculation and Response ---

    // If we failed to get a price from either API
    if (is_null($api_price)) {
        error_log('[API Price Check] Failed to retrieve API price for product ' . $product_id . ($variation_id ? ' (Var: ' . $variation_id . ')' : '') . ' on date ' . $date);
        wp_send_json_error(['message' => 'Price not available for the selected date.']);
        return;
    }

    // Convert retrieved price to float
    $api_price = floatval($api_price);
    if ($cost_of_good !== null) { // Convert cost_of_good to float if it exists
        $cost_of_good = floatval($cost_of_good);
    }

    // Default is no discount
    $discounted_price = $api_price;
    $has_discount = false;
    $discount_amount = 0;

    // Only try to apply discount rules if we have a product ID
    if ($variation_id || $product_id) {
        // error_log('Calculating discounts using original API price: ' . $api_price);
        try_direct_discount_calculation($api_price, $product_id, $variation_id, $quantity, $discounted_price);
    } else {
        error_log('[API Price Check] No product/variation ID provided, cannot calculate discounts.');
        // $discounted_price = $api_price; // Ensure discounted price is set even if calculation skipped
    }

    // Check if we actually have a discount
    $has_discount = round($discounted_price) < round($api_price); // Compare rounded values
    $discount_amount = $has_discount ? round($api_price) - round($discounted_price) : 0;

    // Round the prices to whole numbers
    $original_api_price_rounded = round($api_price); // Keep original API price before discount
    $discounted_price_rounded = round($discounted_price);
    $discount_amount = round($discount_amount); // Recalculate based on rounded numbers

    // Removed log: error_log('Original API price: ' . $api_price . ', Final price: ' . $discounted_price);

    // Return both original (pre-discount) and final discounted prices
    wp_send_json_success([
        'original_price' => $original_api_price_rounded, // This is the price from the API *before* ADP discounts
        'price' => $discounted_price_rounded, // This is the final price *after* ADP discounts
        'cost_of_good' => $cost_of_good !== null ? round($cost_of_good) : null, // Add cost_of_good to response, rounded
        'formatted_price' => wc_price($discounted_price_rounded),
        'has_discount' => $has_discount,
        'discount_amount' => $discount_amount,
        'currency' => get_woocommerce_currency(),
    ]);
}

/**
 * Attempts to calculate the ADP discount percentage for a given product/quantity
 * in the current user context and applies it to the provided API price.
 *
 * @param float $api_price The base price fetched from the external API.
 * @param int $product_id The parent product ID.
 * @param int $variation_id The variation ID (if applicable).
 * @param int $quantity The quantity being considered.
 * @param float &$discounted_price Passed by reference; will be updated with the final price.
 */
function try_direct_discount_calculation($api_price, $product_id, $variation_id, $quantity, &$discounted_price)
{
    // Default to the original API price if no discount is applied or calculable
    $discounted_price = $api_price;

    // We need a product or variation ID to proceed
    if (!$product_id && !$variation_id) {
        error_log('[Discount Calculation] No product ID or variation ID provided. Skipping.');
        return;
    }
    // Prefer variation ID if available
    $the_id = $variation_id ? $variation_id : $product_id;

    // Check if ADP plugin is active
    if (!function_exists('adp_functions')) {
        error_log('[Discount Calculation] ADP plugin function "adp_functions" not found. Cannot calculate discounts.');
        $discounted_price = round($api_price); // Ensure rounding
        return; // Exit if ADP is not active
    }

    try {
        $adp = adp_functions();
        // Get applicable rule objects for the product
        $rules = $adp->getActiveRulesForProduct($the_id, $quantity);

        if (empty($rules)) {
            error_log('[Discount Calculation] No applicable ADP rules found for product #' . $the_id);
            $discounted_price = round($api_price); // Ensure rounding if no rules
            return;
        }

        // --- Logic to extract discount type and value from the first applicable rule ---
        // Note: This assumes the first rule is the primary one. If multiple rules apply,
        // you might need more complex logic to decide which discount takes precedence
        // or how they combine, depending on ADP's behavior and your requirements.
        $discount_applied = false;
        foreach ($rules as $rule) {
            // +++ Add detailed logging for the current rule +++
            $rule_id = method_exists($rule, 'getId') ? $rule->getId() : 'N/A';
            $rule_class = get_class($rule);
            //             error_log("[Discount Calculation] Processing Rule ID: {$rule_id}, Class: {$rule_class}");

            // Check for standard product adjustments (fixed or percentage)
            if ($rule instanceof \ADP\BaseVersion\Includes\Core\Rule\SingleItemRule || $rule instanceof \ADP\BaseVersion\Includes\Core\Rule\PackageRule) {
                // Check if rule type supports product adjustments
                if (method_exists($rule, 'getProductAdjustmentHandler') && ($handler = $rule->getProductAdjustmentHandler())) {
                    if (method_exists($handler, 'getDiscount') && ($discount = $handler->getDiscount())) {
                        if ($discount instanceof \ADP\BaseVersion\Includes\Core\Rule\Structures\Discount) {
                            $discount_type = $discount->getType();
                            $discount_value = $discount->getValue();

                            //                              error_log('[Discount Calculation] Found Rule ID: ' . $rule->getId() . ', Type: ' . $discount_type . ', Value: ' . $discount_value);

                            // Apply discount based on type
                            if ($discount_type === \ADP\BaseVersion\Includes\Core\Rule\Structures\Discount::TYPE_PERCENTAGE) {
                                $discounted_price = $api_price * (1 - $discount_value / 100);
                                //                                 error_log('[Discount Calculation] Applied Percentage Discount (' . $discount_value . '%) to API price (' . $api_price . '). New price: ' . $discounted_price);
                                $discount_applied = true;
                            } elseif ($discount_type === \ADP\BaseVersion\Includes\Core\Rule\Structures\Discount::TYPE_AMOUNT) {
                                // TYPE_AMOUNT is often per cart item line total in ADP, might need quantity adjustment?
                                // Let's assume it applies to the total value of items for now.
                                // Requires verification based on how TYPE_AMOUNT rules are set up.
                                // If it's per item, this calculation is wrong.
                                $discount_per_item = $discount_value / $quantity; // Assuming value is for total qty
                                $discounted_price = $api_price - $discount_per_item;
                                error_log('[Discount Calculation] Applied Fixed Amount Discount (' . $discount_value . ' total / ' . $discount_per_item . ' per item) to API price (' . $api_price . '). New price: ' . $discounted_price);
                                $discount_applied = true;
                            } elseif ($discount_type === \ADP\BaseVersion\Includes\Core\Rule\Structures\Discount::TYPE_AMOUNT_PER_ITEM) {
                                // TYPE_AMOUNT_PER_ITEM directly applies per item
                                $discounted_price = $api_price - $discount_value;
                                //                                  error_log('[Discount Calculation] Applied Fixed Amount Per Item Discount (' . $discount_value . ') to API price (' . $api_price . '). New price: ' . $discounted_price);
                                $discount_applied = true;
                            } elseif ($discount_type === \ADP\BaseVersion\Includes\Core\Rule\Structures\Discount::TYPE_FIXED_VALUE) {
                                // TYPE_FIXED_VALUE sets the price directly, often per item
                                $discounted_price = $discount_value;
                                //                                 error_log('[Discount Calculation] Applied Fixed Price Value (' . $discount_value . '). Original API price (' . $api_price . '). New price: ' . $discounted_price);
                                $discount_applied = true;
                            } else {
                                //                                 error_log('[Discount Calculation] Rule ID: ' . $rule->getId() . ' - Unhandled Discount Type in ProductAdjustmentHandler: ' . $discount_type);
                            }
                        } else {
                            // +++ Log if getDiscount didn't return anything +++
                            //                              error_log("[Discount Calculation] Rule ID: {$rule_id} - Method getDiscount exists on handler but returned null or false.");
                        }
                        // Break after finding the first handler with a discount
                        if ($discount_applied) {
                            break;
                        }
                    }
                }
                // Check for Range Adjustments (Bulk Pricing) if no standard adjustment found yet
                elseif (!$discount_applied && method_exists($rule, 'getProductRangeAdjustmentHandler') && ($range_handler = $rule->getProductRangeAdjustmentHandler())) {
                    // +++ Log entering range handler logic +++
                    //                      error_log("[Discount Calculation] Rule ID: {$rule_id} - Checking RangeAdjustmentHandler.");
                    $handler_class = is_object($range_handler) ? get_class($range_handler) : gettype($range_handler);
                    //                      error_log("[Discount Calculation] Rule ID: {$rule_id} - Range Handler Class: {$handler_class}");

                    // Range adjustments apply based on quantity tiers. We need to find the correct range.
                    if (method_exists($range_handler, 'getRanges')) {
                        $ranges = $range_handler->getRanges();
                        $matched_range_discount = null;
                        foreach ($ranges as $range) {
                            // +++ Log range details +++
                            $range_from = $range->getFrom();
                            $range_to = $range->getTo();
                            $range_data = $range->getData();
                            $range_data_type = is_object($range_data) ? get_class($range_data) : gettype($range_data);
                            //                               error_log("[Discount Calculation] Rule ID: {$rule_id} - Checking range: From={$range_from}, To={$range_to}, Data Type={$range_data_type}, Qty={$quantity}");

                            if ($quantity >= $range->getFrom() && $quantity <= $range->getTo()) {
                                $matched_range_discount = $range->getData();
                                break;
                            }
                        }

                        if ($matched_range_discount instanceof \ADP\BaseVersion\Includes\Core\Rule\Structures\Discount) {
                            $discount_type = $matched_range_discount->getType();
                            $discount_value = $matched_range_discount->getValue();
                            //                              error_log('[Discount Calculation] Found Range Rule ID: ' . $rule->getId() . ', Matched Range (' . $range->getFrom() . '-' . $range->getTo() . '), Type: ' . $discount_type . ', Value: ' . $discount_value);

                            // Apply discount based on type (similar logic as above)
                            if ($discount_type === \ADP\BaseVersion\Includes\Core\Rule\Structures\Discount::TYPE_PERCENTAGE) {
                                $discounted_price = $api_price * (1 - $discount_value / 100);
                                //                                  error_log('[Discount Calculation] Applied Range Percentage Discount (' . $discount_value . '%) to API price (' . $api_price . '). New price: ' . $discounted_price);
                                $discount_applied = true;
                            } elseif ($discount_type === \ADP\BaseVersion\Includes\Core\Rule\Structures\Discount::TYPE_FIXED_VALUE) {
                                // Fixed value in range usually means setting the price per item
                                $discounted_price = $discount_value;
                                //                                  error_log('[Discount Calculation] Applied Range Fixed Price (' . $discount_value . ') to API price (' . $api_price . '). New price: ' . $discounted_price);
                                $discount_applied = true;
                            } else {
                                //                                  error_log('[Discount Calculation] Rule ID: ' . $rule->getId() . ' - Unhandled Discount Type in RangeAdjustmentHandler: ' . $discount_type);
                            }
                        } else {
                            //                               error_log('[Discount Calculation] Rule ID: ' . $rule->getId() . ' - No matching range found or range data is not a Discount object.');
                        }
                    } else {
                        // +++ Log if getRanges doesn't exist +++
                        error_log("[Discount Calculation] Rule ID: {$rule_id} - Method getRanges does not exist on range handler.");
                    }
                    // Break after finding the first handler with a discount
                    if ($discount_applied) {
                        break;
                    }
                } else {
                    // +++ Log if getProductRangeAdjustmentHandler didn't return anything or was skipped +++
                    if (!$discount_applied) {
                        error_log("[Discount Calculation] Rule ID: {$rule_id} - Method getProductRangeAdjustmentHandler does not exist, returned null/false, or was skipped because discount already applied.");
                    }
                }
            } else {
                // +++ Log if rule type doesn't match expected types +++
                error_log("[Discount Calculation] Rule ID: {$rule_id} - Rule type {$rule_class} is not SingleItemRule or PackageRule.");
            }
            // If we found and applied a discount from any handler in this rule, stop checking other rules.
            if ($discount_applied) {
                break;
            }
        } // End foreach rule

        if (!$discount_applied) {
            error_log('[Discount Calculation] No discount was applied from found rules for product #' . $the_id);
        }

        // Ensure price doesn't go below zero and round the final price
        $discounted_price = max(0, round($discounted_price));
        error_log('[Discount Calculation] Final Price for product #' . $the_id . ': ' . $discounted_price);

        // +++ Add check for Role Discounts (Corrected Logic) +++
        if (!$discount_applied && method_exists($rule, 'getRoleDiscounts') && ($role_discounts_array = $rule->getRoleDiscounts())) {
            error_log("[Discount Calculation] Rule ID: {$rule_id} - Checking RoleDiscounts.");
            // error_log("[Discount Calculation] Role Discounts Data: " . print_r($role_discounts_array, true)); // Keep for debugging if needed

            if (!empty($role_discounts_array) && is_array($role_discounts_array)) {
                $current_user = wp_get_current_user();
                $is_guest = !is_user_logged_in(); // Check if user is guest
                $user_roles = (array) $current_user->roles;

                if ($is_guest) {
                    error_log("[Discount Calculation] Rule ID: {$rule_id} - User is Guest. Checking for 'wdp_guest'.");
                } else {
                    error_log("[Discount Calculation] Rule ID: {$rule_id} - Logged-in User Roles: " . implode(', ', $user_roles));
                }

                // Iterate through the numerically indexed RoleDiscount objects
                foreach ($role_discounts_array as $index => $role_discount_obj) {
                    error_log("[Discount Calculation] Rule ID: {$rule_id} - Processing RoleDiscount Object at index {$index}");

                    // Verify structure and get necessary data
                    if (is_object($role_discount_obj) && method_exists($role_discount_obj, 'getRoles') && method_exists($role_discount_obj, 'getDiscount')) {
                        $applicable_roles = (array) $role_discount_obj->getRoles();
                        $discount = $role_discount_obj->getDiscount();
                        error_log("[Discount Calculation] Rule ID: {$rule_id} - Index {$index} Applicable Roles: " . implode(', ', $applicable_roles));

                        $role_match_found = false;
                        // Check for role match based on guest/logged-in status
                        if ($is_guest) {
                            if (in_array('wdp_guest', $applicable_roles)) {
                                $role_match_found = true;
                                error_log("[Discount Calculation] Rule ID: {$rule_id} - Index {$index} Matched Guest Role 'wdp_guest'.");
                            }
                        } else {
                            if (!empty(array_intersect($user_roles, $applicable_roles))) {
                                $role_match_found = true;
                                error_log("[Discount Calculation] Rule ID: {$rule_id} - Index {$index} Matched Logged-in User Role.");
                            }
                        }

                        // If role matched and we have a valid discount object
                        if ($role_match_found && $discount instanceof \ADP\BaseVersion\Includes\Core\Rule\Structures\Discount) {
                            $discount_type = $discount->getType();
                            $discount_value = $discount->getValue();
                            error_log("[Discount Calculation] Rule ID: {$rule_id} - Index {$index} Found Role Discount: Type={$discount_type}, Value={$discount_value}");

                            // Apply discount based on type (copied logic)
                            if ($discount_type === \ADP\BaseVersion\Includes\Core\Rule\Structures\Discount::TYPE_PERCENTAGE) {
                                $discounted_price = $api_price * (1 - $discount_value / 100);
                                error_log('[Discount Calculation] Applied Role Percentage Discount (' . $discount_value . '%) to API price (' . $api_price . '). New price: ' . $discounted_price);
                                $discount_applied = true;
                            } elseif ($discount_type === \ADP\BaseVersion\Includes\Core\Rule\Structures\Discount::TYPE_AMOUNT) {
                                $discount_per_item = $discount_value / $quantity;
                                $discounted_price = $api_price - $discount_per_item;
                                error_log('[Discount Calculation] Applied Role Fixed Amount Discount (' . $discount_value . ' total / ' . $discount_per_item . ' per item) to API price (' . $api_price . '). New price: ' . $discounted_price);
                                $discount_applied = true;
                            } elseif ($discount_type === \ADP\BaseVersion\Includes\Core\Rule\Structures\Discount::TYPE_AMOUNT_PER_ITEM) {
                                $discounted_price = $api_price - $discount_value;
                                error_log('[Discount Calculation] Applied Role Fixed Amount Per Item Discount (' . $discount_value . ') to API price (' . $api_price . '). New price: ' . $discounted_price);
                                $discount_applied = true;
                            } elseif ($discount_type === \ADP\BaseVersion\Includes\Core\Rule\Structures\Discount::TYPE_FIXED_VALUE) {
                                $discounted_price = $discount_value;
                                error_log('[Discount Calculation] Applied Role Fixed Price Value (' . $discount_value . '). Original API price (' . $api_price . '). New price: ' . $discounted_price);
                                $discount_applied = true;
                            } else {
                                error_log('[Discount Calculation] Rule ID: ' . $rule->getId() . ' - Unhandled Discount Type in RoleDiscount: ' . $discount_type);
                            }
                        } elseif ($role_match_found) {
                            error_log("[Discount Calculation] Rule ID: {$rule_id} - Index {$index} Role matched but getDiscount() did not return a valid Discount object.");
                        }
                    } else {
                        error_log("[Discount Calculation] Rule ID: {$rule_id} - Index {$index} RoleDiscount object structure invalid or methods missing.");
                    }

                    // If discount applied for this RoleDiscount object, break inner loop
                    if ($discount_applied) {
                        break;
                    }
                } // End foreach role_discount_object
            } else {
                error_log("[Discount Calculation] Rule ID: {$rule_id} - getRoleDiscounts() did not return a non-empty array.");
            }
            // If discount applied, break outer loop (handled below)
        }
        // -- End Role Discount Check ---
        else {
            // +++ Log if getProductRangeAdjustmentHandler/getRoleDiscounts didn't return anything or was skipped +++
            if (!$discount_applied) {
                error_log("[Discount Calculation] Rule ID: {$rule_id} - No applicable ProductAdjustment, RangeAdjustment, or RoleDiscount handler found or matched.");
            }
        }
    } catch (Exception $e) {
        // Catch and log any errors during the ADP rule fetching/processing
        error_log('[Discount Calculation] Error during ADP rule processing for product #' . $the_id . ': ' . $e->getMessage());
        // Fallback to the original API price in case of errors
        $discounted_price = round($api_price);
    }
}

add_action('wp_enqueue_scripts', 'enqueue_custom_variation_script');
function enqueue_custom_variation_script()
{
    wp_enqueue_script(
        'custom-variations',
        get_stylesheet_directory_uri() . '/js/custom-variation.js',
        ['jquery', 'wc-add-to-cart-variation'],
        '1.1', // Incremented version
        true
    );

    // Add API price nonce for AJAX
    wp_localize_script('custom-variations', 'api_price_vars', [
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('api_price_nonce'),
    ]);

    // Add CSS for date picker and API price loading
    wp_add_inline_style(
        'woocommerce-inline',
        '
        .custom-date-field {
            margin-bottom: 1.5em;
            clear: both;
            display: flex;
            flex-direction: column;
        }

        .custom-date-field label {
            margin-bottom: 0.5em;
            font-weight: bold;
        }

        .custom-date-field input[type="date"] {
            padding: 0.5em;
            border: 1px solid #ddd;
            border-radius: 3px;
            margin-bottom: 0.5em;
        }

        #api-price-loading {
            font-style: italic;
            color: #666;
            margin-top: 0.5em;
            display: none;
        }
    '
    );
}

// Override the variation price if API price exists (Potentially redundant)
add_filter('woocommerce_product_variation_get_price', 'override_variation_price_with_api', 99, 2);
function override_variation_price_with_api($price, $product)
{
    global $woocommerce;

    // Only override in cart/checkout context where wdp_calculate_base_price might not apply yet
    // Let wdp_calculate_base_price handle the actual cart calculation price
    if (!is_cart() && !is_checkout() && !(defined('DOING_AJAX') && DOING_AJAX)) {
        return $price;
    }

    // Check if the product is in cart with an API price
    if (WC()->cart) {
        // Check if cart exists
        foreach (WC()->cart->get_cart() as $cart_item) {
            if (isset($cart_item['variation_id']) && $cart_item['variation_id'] == $product->get_id() && isset($cart_item['api_price'])) {
                return $cart_item['api_price'];
            }
        }
    }

    return $price;
}

// Show admin notice if variations are missing API pricing fields
add_action('admin_notices', 'check_variations_for_api_pricing');
function check_variations_for_api_pricing()
{
    // Only show on product edit screen
    $screen = get_current_screen();
    if (!$screen || !in_array($screen->id, ['product', 'edit-product'])) {
        // Check on edit screen and list screen
        return;
    }

    // Get the product ID (works on edit screen)
    $product_id = isset($_GET['post']) ? intval($_GET['post']) : 0;

    // If on list screen, we might need a different approach or skip this check
    if (!$product_id && $screen->id === 'edit-product') {
        // Maybe loop through published variable products? Could be slow. Skip for now.
        return;
    }

    if (!$product_id) {
        return;
    }

    // Check if it's a variable product
    $product = wc_get_product($product_id);
    if (!$product || !$product->is_type('variable')) {
        return;
    }

    // Determine product type
    $is_vin_product = $product->get_meta('vin_product', true) === 'yes';
    $is_sunworld_product = $product->get_meta('sunworld_product', true) === 'yes';

    // Check variations based on product type
    $variations = $product->get_children(); // Get variation IDs
    $missing_fields = false;
    $required_fields_text = '';

    if (!empty($variations)) {
        foreach ($variations as $variation_id) {
            $variation_obj = wc_get_product($variation_id);
            if (!$variation_obj) {
                continue;
            }

            $site_code = $variation_obj->get_meta('site_code');

            if ($is_sunworld_product) {
                $product_code = $variation_obj->get_meta('product_code');
                $required_fields_text = 'Site Code, Product Code';
                if (empty($site_code) || empty($product_code)) {
                    $missing_fields = true;
                    break; // Found one missing
                }
            } elseif ($is_vin_product) {
                $service_code = $variation_obj->get_meta('service_code');
                $rate_code = $variation_obj->get_meta('rate_code');
                $required_fields_text = 'Site Code, Service Code, Rate Code';
                if (empty($site_code) || empty($service_code) || empty($rate_code)) {
                    $missing_fields = true;
                    break; // Found one missing
                }
            } else {
                // Not a tracked product type, skip check? Or assume default?
                // For now, let's skip the check if neither meta is set.
                $required_fields_text = 'Site Code, Service Code, Rate Code'; // Default assumption for notice
                $service_code = $variation_obj->get_meta('service_code');
                $rate_code = $variation_obj->get_meta('rate_code');
                if (empty($site_code) || empty($service_code) || empty($rate_code)) {
                    $missing_fields = true;
                    break; // Found one missing
                }
            }
        }
    }

    if ($missing_fields) {
        echo '<div class="notice notice-warning is-dismissible">
            <p><strong>API Pricing Configuration Required:</strong> Some product variations for "' .
            esc_html($product->get_name()) .
            '" are missing the required API pricing fields (' .
            esc_html($required_fields_text) .
            '). Dynamic pricing may not work correctly.</p>
        </div>';
    }
}

// Debug function to check if fields are correctly set
add_action('wp_footer', 'debug_api_pricing_fields');
function debug_api_pricing_fields()
{
    // Only show in product pages
    if (!is_product()) {
        return;
    }

    global $product;
    if (!$product || !$product->is_type('variable')) {
        return;
    }

    // Only show to admin users
    if (!current_user_can('manage_options')) {
        return;
    }

    echo '<div style="background: #f8f8f8; padding: 10px; margin: 20px 0; border: 1px solid #ddd; clear:both;">';
    echo '<h4>Debug Info: Variation API Codes (Admin Only)</h4>';

    // Determine parent product type
    $is_vin_product = $product->get_meta('vin_product', true) === 'yes';
    $is_sunworld_product = $product->get_meta('sunworld_product', true) === 'yes';
    $product_type_info = '';
    if ($is_sunworld_product) {
        $product_type_info = ' (Sunworld Product - Requires Site Code, Product Code)';
    } elseif ($is_vin_product) {
        $product_type_info = ' (Vinpearl Product - Requires Site Code, Service Code, Rate Code)';
    }
    echo '<p><em>Product Type: ' . esc_html($product_type_info) . '</em></p>';

    $variations = $product->get_available_variations('objects'); // Get variation objects directly

    if (empty($variations)) {
        echo '<p>No available variations found for this product.</p>';
    } else {
        foreach ($variations as $variation_obj) {
            $variation_name = $variation_obj->get_formatted_name();

            $site_code = $variation_obj->get_meta('site_code');
            $service_code = $variation_obj->get_meta('service_code');
            $rate_code = $variation_obj->get_meta('rate_code');
            $product_code = $variation_obj->get_meta('product_code');

            echo '<p>';
            echo '<strong>' . esc_html($variation_name) . '</strong> (ID: ' . $variation_obj->get_id() . ')<br>';

            // Site Code (Always required)
            echo 'Site Code: ' . ($site_code ? esc_html($site_code) : '<span style="color:red">Missing</span>') . '<br>';

            // Sunworld specific
            if ($is_sunworld_product) {
                echo 'Product Code: ' . ($product_code ? esc_html($product_code) : '<span style="color:red">Missing</span>') . '<br>';
                // Display others as N/A or greyed out for clarity
                echo '<span style="color: #999;">Service Code: (N/A for Sunworld)</span><br>';
                echo '<span style="color: #999;">Rate Code: (N/A for Sunworld)</span>';
            }
            // Vinpearl specific (or default if neither)
            elseif ($is_vin_product) {
                echo 'Service Code: ' . ($service_code ? esc_html($service_code) : '<span style="color:red">Missing</span>') . '<br>';
                echo 'Rate Code: ' . ($rate_code ? esc_html($rate_code) : '<span style="color:red">Missing</span>') . '<br>';
                echo '<span style="color: #999;">Product Code: (N/A for Vinpearl)</span>';
            } else {
                // If neither type, show all but don't highlight as missing (unless standard WC requires them?)
                echo 'Service Code: ' . esc_html($service_code) . ' (Type Unknown)<br>';
                echo 'Rate Code: ' . esc_html($rate_code) . ' (Type Unknown)<br>';
                echo 'Product Code: ' . esc_html($product_code) . ' (Type Unknown)';
            }

            echo '</p>';
        }
    }

    echo '</div>';
}

// Override the product prices for cart calculations - Tell ADP the base price
add_filter('woocommerce_before_calculate_totals', 'apply_api_prices_to_cart', 20);
function apply_api_prices_to_cart($cart)
{
    // Check if cart object is valid
    if (!is_object($cart) || !isset($cart->cart_contents)) {
        return;
    }

    if (is_admin() && !defined('DOING_AJAX')) {
        return;
    }

    // Loop through cart items
    foreach ($cart->get_cart() as $cart_item_key => $cart_item) {
        // Check if the item has our API price and the data object exists
        if (isset($cart_item['api_price']) && isset($cart_item['data']) && is_object($cart_item['data'])) {
            // Use the original API price as the base price
            $api_price = floatval($cart_item['api_price']);

            // Set the price in cart item data - this is the original API price
            // ADP will apply discounts to this price via the wdp_calculate_base_price filter
            $cart_item['data']->set_price($api_price);

            // Removed log: error_log('Set cart item base price to API price: ' . $api_price . ' (ADP will apply discounts)');
        }
    }
}

// Direct hook into the advanced-dynamic-pricing-for-woocommerce plugin - Confirm base price for ADP
add_filter(
    'wdp_calculate_base_price',
    function ($price, $cart_item) {
        if (isset($cart_item['api_price'])) {
            // Removed log: error_log('ADP base price calculation intercepted - using API price: ' . $cart_item['api_price']);
            // Return the unmodified API price stored during add-to-cart
            return floatval($cart_item['api_price']);
        }
        // Otherwise, return the price ADP calculated initially
        return $price;
    },
    10,
    2
); // Priority 10 should be fine, but ensure it runs before ADP's main calcs if issues arise

// Override product prices in cart display (Potentially redundant - review if needed)
add_filter('woocommerce_cart_item_price', 'override_cart_item_price', 9999, 3);
function override_cart_item_price($price_html, $cart_item, $cart_item_key)
{
    // Prioritize showing the final discounted price if available
    if (isset($cart_item['api_discounted_price']) && is_numeric($cart_item['api_discounted_price'])) {
        return wc_price($cart_item['api_discounted_price']);
    }
    // Fallback to original API price if discounted one isn't set
    elseif (isset($cart_item['api_price']) && is_numeric($cart_item['api_price'])) {
        return wc_price($cart_item['api_price']);
    }
    return $price_html;
}

// Override subtotal display (Potentially redundant - review if needed)
add_filter('woocommerce_cart_item_subtotal', 'override_cart_item_subtotal', 9999, 3);
function override_cart_item_subtotal($subtotal, $cart_item, $cart_item_key)
{
    // Prioritize using the final discounted price for subtotal
    $price_to_use = null;
    if (isset($cart_item['api_discounted_price']) && is_numeric($cart_item['api_discounted_price'])) {
        $price_to_use = $cart_item['api_discounted_price'];
    }
    // Fallback to original API price if discounted one isn't set
    elseif (isset($cart_item['api_price']) && is_numeric($cart_item['api_price'])) {
        $price_to_use = $cart_item['api_price'];
    }

    if (!is_null($price_to_use)) {
        $quantity = $cart_item['quantity'];
        return wc_price($price_to_use * $quantity);
    }

    return $subtotal;
}

?>
