jQuery(document).ready(function ($) {
  // Only run if date field exists
  if ($("#custom-date").length) {
    // Track current variation data and state
    let currentVariationData = null;
    let isProcessingApiRequest = false;
    let lastFailedDate = null;
    let apiErrorCount = 0;
    const MAX_API_ERRORS = 3;
    let currentApiRequestXhr = null; // <-- Add this variable to track the request

    // --- Helper Functions ---
    // disableAddToCart, visuallyEnableAddToCart, checkAndSetButtonState, updateDisplayedPrice, handleApiError, wc_format_price
    // (Keep these helper functions as they were)

    function disableAddToCart(message) {
      const $button = $("button.single_add_to_cart_button");
      const alreadyDisabled = $button.prop("disabled");
      $button.prop("disabled", true).addClass("disabled");

      if (!alreadyDisabled || message) {
        if (message) {
          $(".date-required-notice").text(message).show();
        } else {
          $(".date-required-notice").text("Select variation/date or check price.").show();
        }
        $("#api-price-input").val("");
        $("#api-original-price-input").val("");
        $("#api-discounted-price-input").val("");
      }
    }

    function visuallyEnableAddToCart() {
      $("button.single_add_to_cart_button").prop("disabled", false).removeClass("disabled");
      $(".date-required-notice").hide();
    }

    function checkAndSetButtonState() {
      const selectedDate = $("#custom-date").val();
      const apiPriceValue = $("#api-price-input").val();
      const apiPriceSet = apiPriceValue !== "";
      const variationSelected = !!currentVariationData;
      const processing = isProcessingApiRequest; // Use the flag still, indicates intent
      const failedDateMatch = selectedDate === lastFailedDate;
      const errorLimitReached = apiErrorCount >= MAX_API_ERRORS;

      // Enable only if we have variation, date, price, not processing, no recent failure for this date, and within error limits
      if (variationSelected && selectedDate && apiPriceSet && !processing && !failedDateMatch && !errorLimitReached) {
        visuallyEnableAddToCart();
      } else {
        let reason = "Checking price..."; // Default reason while processing
        if (processing) reason = "Checking price..."; // Explicitly set if processing flag is true
        else if (!variationSelected) reason = "Please select variation options.";
        else if (!selectedDate) reason = "Please select a date.";
        else if (failedDateMatch) reason = "Price not available for selected date.";
        else if (errorLimitReached) reason = "Error checking price. Please refresh.";
        // If we have date/variation but no price (and not processing/failed/error limit), implies price check needed or failed
        else if (!apiPriceSet && selectedDate && variationSelected) reason = "Price check needed or failed.";

        disableAddToCart(reason);
      }
    }

    function updateDisplayedPrice(formattedPrice, originalPriceForCart, discountedRefPrice) {
      $(".woocommerce-variation-price").html('<span class="price">' + formattedPrice + "</span>");

      const $form = $(".variations_form");
      if ($form.find("#api-price-input").length === 0) $form.append('<input type="hidden" name="api_price" id="api-price-input" value="">');
      if ($form.find("#api-original-price-input").length === 0)
        $form.append('<input type="hidden" name="api_original_price" id="api-original-price-input" value="">');
      if ($form.find("#api-discounted-price-input").length === 0)
        $form.append('<input type="hidden" name="api_discounted_price" id="api-discounted-price-input" value="">');

      $("#api-price-input").val(originalPriceForCart);
      $("#api-original-price-input").val(originalPriceForCart);
      $("#api-discounted-price-input").val(discountedRefPrice);

      $(".woocommerce-variation").show();
      $(".single_variation_wrap").show();

      $(document.body).trigger("adp_product_price_updated", [originalPriceForCart, currentVariationData]);
    }

    function handleApiError(errorMessage, failedDate) {
      // Don't reset isProcessingApiRequest here, let the 'complete' or 'error' handler do it.
      apiErrorCount++;
      lastFailedDate = failedDate;

      let alertMessage = "Unable to get price for the selected date (" + failedDate + "). Please try another date or contact support.";
      if (apiErrorCount >= MAX_API_ERRORS) {
        alertMessage = "We're having trouble checking the price. Please refresh the page or contact support if the problem persists.";
      }
      alert(alertMessage); // Consider replacing alert

      disableAddToCart("Price not available for selected date.");
      $("#api-price-loading").hide(); // Hide loading indicator on error
    }

    function wc_format_price(price) {
      try {
        const settings = typeof woocommerce_params !== "undefined" ? woocommerce_params : {};
        const symbol = settings.currency_format_symbol || (settings.currency && settings.currency.symbol) || "₫";
        const thousandSeparator = settings.currency_format_thousand_sep || ",";
        const decimalSeparator = settings.currency_format_decimal_sep || ".";
        const numDecimals = settings.currency_format_num_decimals !== undefined ? settings.currency_format_num_decimals : 0;
        const format = settings.currency_format || "%s%v";

        let priceString = parseFloat(price).toFixed(numDecimals).toString();
        const parts = priceString.split(".");
        parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator);
        priceString = parts.join(decimalSeparator);

        let formattedPrice = format.replace("%s", symbol).replace("%v", priceString);
        return formattedPrice;
      } catch (e) {
        // Fallback basic formatting
        return (
          "₫" +
          parseFloat(price)
            .toFixed(0)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ",")
        );
      }
    }

    // --- Event Handlers ---

    $(".variations_form").on("show_variation", function (event, variation) {
      let reliableVariationData = null;
      const variationId = variation.variation_id;

      if (variationId) {
        const allVariationsData = $(this).data("product_variations");
        if (allVariationsData) {
          reliableVariationData = allVariationsData.find((v) => v.variation_id == variationId);
          if (!reliableVariationData) {
            reliableVariationData = variation;
          }
        } else {
          reliableVariationData = variation;
        }
      } else {
        reliableVariationData = variation;
      }

      currentVariationData = reliableVariationData;
      apiErrorCount = 0;
      lastFailedDate = null;

      const selectedDate = $("#custom-date").val();

      $(".woocommerce-variation-price").empty();
      $("#api-price-input").val("");
      $("#api-original-price-input").val("");
      $("#api-discounted-price-input").val("");

      checkAndSetButtonState(); // Update button state immediately

      // Fetch price if date is selected
      if (selectedDate && currentVariationData) {
        // Check if the needed custom codes exist before fetching
        if (currentVariationData.site_code && currentVariationData.service_code && currentVariationData.rate_code) {
          fetchApiPrice(selectedDate, currentVariationData);
        } else {
          // Abort any pending request as the new variation details are bad
          if (currentApiRequestXhr) {
            currentApiRequestXhr.abort();
          }
          isProcessingApiRequest = false; // Ensure flag is reset
          $("#api-price-loading").hide();
          disableAddToCart("Variation details incomplete. Cannot check price.");
        }
      } else if (!selectedDate && currentVariationData) {
        disableAddToCart("Please select a date.");
        // Abort any pending request if user deselects date after selecting variation
        if (currentApiRequestXhr) {
          currentApiRequestXhr.abort();
          isProcessingApiRequest = false;
          $("#api-price-loading").hide();
        }
      } else {
        // No action needed here, checkAndSetButtonState handles button
        // Abort pending requests if variation becomes invalid etc.
        if (currentApiRequestXhr) {
          currentApiRequestXhr.abort();
          isProcessingApiRequest = false;
          $("#api-price-loading").hide();
        }
      }
    });

    $(".variations_form").on("hide_variation", function () {
      // Abort any pending request if user deselects variation attributes
      if (currentApiRequestXhr) {
        currentApiRequestXhr.abort();
      }
      currentVariationData = null;
      lastFailedDate = null;
      isProcessingApiRequest = false; // Ensure flag is reset
      $("#api-price-loading").hide(); // Hide loading
      $(".woocommerce-variation-price").empty();
      $("#api-price-input").val("");
      $("#api-original-price-input").val("");
      $("#api-discounted-price-input").val("");
      $(".woocommerce-variation").hide();
      checkAndSetButtonState();
    });

    $("#custom-date").on("change", function () {
      const selectedDate = $(this).val();

      // Attempt recovery if needed
      if (!currentVariationData) {
        const selectedVariationId = $(".variations_form input[name=variation_id]").val();
        if (selectedVariationId && selectedVariationId !== "0") {
          const variationFormData = $(".variations_form").data("product_variations");
          const recoveredVariation = variationFormData ? variationFormData.find((v) => v.variation_id == selectedVariationId) : null;
          if (recoveredVariation) {
            currentVariationData = recoveredVariation;
          }
        }
      }

      // Clear previous price on date change
      $(".woocommerce-variation-price").empty();
      $("#api-price-input").val("");
      $("#api-original-price-input").val("");
      $("#api-discounted-price-input").val("");

      if (selectedDate === lastFailedDate) {
        checkAndSetButtonState();
        return;
      }
      if (selectedDate !== lastFailedDate) {
        lastFailedDate = null; // Reset failure only if date is different
      }
      if (apiErrorCount >= MAX_API_ERRORS) {
        checkAndSetButtonState();
        return;
      }

      // Call fetch if date and variation are selected
      if (selectedDate && currentVariationData) {
        fetchApiPrice(selectedDate, currentVariationData);
      } else {
        // Abort any pending request if date is cleared or variation missing
        if (currentApiRequestXhr) {
          currentApiRequestXhr.abort();
        }
        isProcessingApiRequest = false; // Ensure flag is reset
        $("#api-price-loading").hide();
        checkAndSetButtonState();
      }
    });

    $("input.qty").on("change", function () {
      const selectedDate = $("#custom-date").val();

      // Attempt recovery if needed
      if (!currentVariationData) {
        const selectedVariationId = $(".variations_form input[name=variation_id]").val();
        if (selectedVariationId && selectedVariationId !== "0") {
          const variationFormData = $(".variations_form").data("product_variations");
          const recoveredVariation = variationFormData ? variationFormData.find((v) => v.variation_id == selectedVariationId) : null;
          if (recoveredVariation) {
            currentVariationData = recoveredVariation;
          }
        }
      }

      // Clear previous price on quantity change (optional, but safer)
      $(".woocommerce-variation-price").empty();
      $("#api-price-input").val("");
      $("#api-original-price-input").val("");
      $("#api-discounted-price-input").val("");

      // Don't refetch if conditions aren't met
      if (selectedDate === lastFailedDate || apiErrorCount >= MAX_API_ERRORS) {
        checkAndSetButtonState();
        return;
      }

      // Call fetch if date and variation selected
      if (selectedDate && currentVariationData) {
        lastFailedDate = null; // Changing quantity might allow a previously failed date to work
        fetchApiPrice(selectedDate, currentVariationData);
      } else {
        // Abort pending request if state is invalid
        if (currentApiRequestXhr) {
          currentApiRequestXhr.abort();
        }
        isProcessingApiRequest = false; // Ensure flag is reset
        $("#api-price-loading").hide();
        checkAndSetButtonState();
      }
    });

    // --- Price Fetching Function ---
    function fetchApiPrice(date, variation) {
      // --- Abort existing request if any ---
      if (currentApiRequestXhr) {
        currentApiRequestXhr.abort();
        // isProcessingApiRequest should be set to false by the aborted request's error/complete handler.
      }
      // --- End Abort ---

      // **Critical:** Check variation details *after* potential abort logic
      let requiredDetailsMissing = false;
      let missingDetailMessage = "Variation details incomplete.";

      if (!variation || !variation.variation_id || !variation.site_code) {
        requiredDetailsMissing = true; // Basic details always needed
        missingDetailMessage = "Missing variation or site code.";
      } else if (variation.is_sunworld_product) {
        // Sunworld requires site_code and product_code
        if (!variation.product_code) {
          requiredDetailsMissing = true;
          missingDetailMessage = "Missing Product Code for Sunworld variation.";
        }
      } else {
        // Vinpearl requires site_code, service_code, and rate_code
        if (!variation.service_code || !variation.rate_code) {
          requiredDetailsMissing = true;
          missingDetailMessage = "Missing Service/Rate Code for Vinpearl variation.";
        }
      }

      if (requiredDetailsMissing) {
        // Don't set processing true, just disable button and return
        disableAddToCart(missingDetailMessage);
        $("#api-price-loading").hide(); // Ensure loading hidden
        return;
      }

      // Set processing state *now* for the new request
      isProcessingApiRequest = true;
      $("#api-price-loading").show();
      disableAddToCart("Checking price availability..."); // Disable with appropriate message

      const quantity = parseInt($("input.qty").val()) || 1;
      const $form = $(".variations_form");
      const productId = $form.find("input[name='product_id']").val() || $form.data("product_id");

      if (!productId) {
        handleApiError("Internal error: Product ID missing.", date);
        // Reset state manually AFTER calling error handler
        isProcessingApiRequest = false;
        currentApiRequestXhr = null; // Ensure cleared
        $("#api-price-loading").hide();
        checkAndSetButtonState(); // Re-evaluate button state
        return;
      }

      const requestData = {
        action: "get_api_price",
        security: api_price_vars.nonce,
        date: date,
        site_code: variation.site_code,
        service_code: variation.service_code,
        rate_code: variation.rate_code,
        product_code: variation.product_code,
        is_sunworld: variation.is_sunworld_product,
        quantity: quantity,
        variation_id: variation.variation_id,
        product_id: productId,
      };

      // Store the request object
      currentApiRequestXhr = $.ajax({
        url: api_price_vars.ajax_url,
        type: "POST",
        data: requestData,
        success: function (response) {
          // Check if this request is still the relevant one (optional but safer)
          if (
            response.success &&
            response.data &&
            typeof response.data.original_price !== "undefined" &&
            typeof response.data.price !== "undefined"
          ) {
            lastFailedDate = null; // Success, so clear failure state for this date
            apiErrorCount = 0; // Reset error count on success

            const originalPrice = response.data.original_price;
            const finalDiscountedPrice = response.data.price;
            let displayHtml = response.data.formatted_price;

            if (response.data.has_discount) {
              const originalFormatted = wc_format_price(originalPrice);
              displayHtml =
                "<del>" +
                originalFormatted +
                '</del> <span class="discounted-price" style="color: #FF8C00;">' +
                response.data.formatted_price +
                "</span>";
            }
            updateDisplayedPrice(displayHtml, originalPrice, finalDiscountedPrice);
            // Don't visually enable here directly, let checkAndSetButtonState handle it
          } else {
            let errorMsg = "Price data missing in response.";
            if (response.data && response.data.message) errorMsg = response.data.message;
            else if (!response.success) errorMsg = "API Error: Price not available.";
            handleApiError(errorMsg, date);
          }
        },
        error: function (xhr, status, error) {
          // Check if the error is due to abort()
          if (status !== "abort") {
            // Handle actual errors
            handleApiError("Error communicating with server: " + status, date);
          } else {
            // Just log the abort internally if needed, don't show error messages to user
          }
          // State is reset in the 'complete' handler
        },
        complete: function (xhr, status) {
          isProcessingApiRequest = false; // Request finished (success, error, or abort)
          currentApiRequestXhr = null; // Clear the stored XHR object
          $("#api-price-loading").hide(); // Hide loading indicator
          checkAndSetButtonState(); // <-- Crucial: Re-evaluate button state AFTER request completes
        },
      });
    }

    // --- Initialization ---
    const $variationForm = $(".variations_form");
    if ($variationForm.length) {
      if ($variationForm.find("#api-price-input").length === 0)
        $variationForm.append('<input type="hidden" name="api_price" id="api-price-input" value="">');
      if ($variationForm.find("#api-original-price-input").length === 0)
        $variationForm.append('<input type="hidden" name="api_original_price" id="api-original-price-input" value="">');
      if ($variationForm.find("#api-discounted-price-input").length === 0)
        $variationForm.append('<input type="hidden" name="api_discounted_price" id="api-discounted-price-input" value="">');
    }

    checkAndSetButtonState(); // Initial state

    // --- Initial Load Check (Delayed) ---
    setTimeout(function () {
      const initialSelectedDate = $("#custom-date").val();
      const initialSelectedVariationId = $(".variations_form input[name=variation_id]").val();

      if (initialSelectedDate && initialSelectedVariationId && initialSelectedVariationId !== "0") {
        const variationFormData = $(".variations_form").data("product_variations");
        const initialSelectedVariation = variationFormData ? variationFormData.find((v) => v.variation_id == initialSelectedVariationId) : null;

        if (initialSelectedVariation) {
          currentVariationData = initialSelectedVariation;
          // Check if essential codes exist based on product type
          let initialCheckPassed = false;
          if (currentVariationData.is_sunworld_product) {
            if (currentVariationData.site_code && currentVariationData.product_code) {
              initialCheckPassed = true;
            }
          } else {
            if (currentVariationData.site_code && currentVariationData.service_code && currentVariationData.rate_code) {
              initialCheckPassed = true;
            }
          }

          if (initialCheckPassed) {
            fetchApiPrice(initialSelectedDate, currentVariationData); // Fetch price on initial load if valid
          } else {
            disableAddToCart("Variation details incomplete.");
            checkAndSetButtonState();
          }
        } else {
          checkAndSetButtonState();
        }
      } else {
        checkAndSetButtonState();
      }
    }, 600); // Delay remains

    // --- Submit handler ---
    $(".variations_form").on("submit", function (e) {
      const apiPriceField = $("#api-price-input");
      const originalPrice = apiPriceField.val();
      const $button = $("button.single_add_to_cart_button");

      // Prevent submission if button is disabled (relies on checkAndSetButtonState being accurate)
      if ($button.is(":disabled")) {
        e.preventDefault();
        // Optionally provide user feedback
        // alert("Please ensure you have selected a valid date and variation with an available price.");
        return false;
      }

      // Double-check essential fields just before submitting
      if (!$("#custom-date").val()) {
        e.preventDefault();
        disableAddToCart("Please select a date."); // Ensure message shown
        return false;
      }
      if (!currentVariationData || !currentVariationData.variation_id) {
        e.preventDefault();
        disableAddToCart("Please select variation options."); // Ensure message shown
        return false;
      }
      if (!apiPriceField.length || !originalPrice) {
        e.preventDefault();
        disableAddToCart("Price check needed or failed."); // Ensure message shown
        return false;
      }
    });
  } // End if #custom-date exists
});
