jQuery(document).ready(function ($) {
    $(document).on('click', '.woocommerce-save-button', function (e) {
        e.preventDefault();

        const order_id = parseInt(new URLSearchParams(window.location.search).get('post'));
        const new_status = $('#order_status').val();
        const nonce = $('#_wpnonce').val();

        if (new_status === 'wc-completed') {
            $.ajax({
                type: 'POST',
                url: ajax_object.ajax_url,
                data: {
                    action: 'woocommerce_mark_order_status',
                    order_id: order_id,
                    status: 'completed',
                    _wpnonce: nonce,
                },
                success: function () {
                    // Handle success here
                },
                error: function () {
                    // Handle error here
                },
            });
        } else {
            $(this).closest('form').submit();
        }
    });
});