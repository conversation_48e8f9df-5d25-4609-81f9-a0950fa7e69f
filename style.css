/*
Theme Name: Flatsome Child
Description: This is a child theme for Flatsome Theme
Author: UX Themes
Template: flatsome
Version: 3.0
*/

/*************** ADD CUSTOM CSS HERE.   ***************/
/* Hide the shipping fields and "Ship to a different address?" checkbox */
.woocommerce-shipping-fields,
.woocommerce-shipping-toggle {
    display: none !important;
}

/* Hide redundant individual price span in WC Blocks Checkout Order Summary */
.woocommerce-checkout .wp-block-woocommerce-checkout-order-summary-block span.wc-block-components-order-summary-item__individual-prices.price.wc-block-components-product-price {
    display: none !important; /* Use !important to ensure override */
}

@media only screen and (max-width: 48em) {
/*************** ADD MOBILE ONLY CSS HERE  ***************/


}